{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"vys.Tests/1.0.0": {"dependencies": {"Microsoft.NET.Test.Sdk": "17.12.0", "Moq": "4.20.72", "coverlet.collector": "6.0.2", "vys": "1.0.0", "xunit": "2.9.2", "xunit.runner.visualstudio": "2.8.2", "Microsoft.Web.WebView2.Core": "1.0.2792.45", "Microsoft.Web.WebView2.WinForms": "1.0.2792.45", "Microsoft.Web.WebView2.Wpf": "1.0.2792.45"}, "runtime": {"vys.Tests.dll": {}}}, "Castle.Core/5.1.1": {"dependencies": {"System.Diagnostics.EventLog": "6.0.0"}, "runtime": {"lib/net6.0/Castle.Core.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.1.0"}}}, "coverlet.collector/6.0.2": {}, "Hardcodet.NotifyIcon.Wpf/1.1.0": {"runtime": {"lib/net5.0-windows7.0/Hardcodet.NotifyIcon.Wpf.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Microsoft.CodeCoverage/17.12.0": {"runtime": {"lib/netcoreapp3.1/Microsoft.VisualStudio.CodeCoverage.Shim.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.524.48002"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.NET.Test.Sdk/17.12.0": {"dependencies": {"Microsoft.CodeCoverage": "17.12.0", "Microsoft.TestPlatform.TestHost": "17.12.0"}}, "Microsoft.NETCore.Platforms/3.1.0": {}, "Microsoft.TestPlatform.ObjectModel/17.12.0": {"dependencies": {"System.Reflection.Metadata": "1.6.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.TestPlatform.TestHost/17.12.0": {"dependencies": {"Microsoft.TestPlatform.ObjectModel": "17.12.0", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CommunicationUtilities.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}, "lib/netcoreapp3.1/Microsoft.TestPlatform.CrossPlatEngine.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}, "lib/netcoreapp3.1/Microsoft.TestPlatform.Utilities.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.Common.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}, "lib/netcoreapp3.1/testhost.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Web.WebView2/1.0.2792.45": {"runtimeTargets": {"runtimes/win-arm64/native/WebView2Loader.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "1.0.2792.45"}, "runtimes/win-x64/native/WebView2Loader.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.0.2792.45"}, "runtimes/win-x86/native/WebView2Loader.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.0.2792.45"}}}, "Microsoft.Web.WebView2.DevToolsProtocolExtension/1.0.2901": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.Web.WebView2": "1.0.2792.45", "System.Text.Json": "6.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Web.WebView2.DevToolsProtocolExtension.dll": {"assemblyVersion": "1.0.2901.0", "fileVersion": "1.0.2901.0"}}}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}}, "Microsoft.Win32.SystemEvents/9.0.0": {}, "Moq/4.20.72": {"dependencies": {"Castle.Core": "5.1.1"}, "runtime": {"lib/net6.0/Moq.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "NAudio/2.2.1": {"dependencies": {"NAudio.Asio": "2.2.1", "NAudio.Core": "2.2.1", "NAudio.Midi": "2.2.1", "NAudio.Wasapi": "2.2.1", "NAudio.WinForms": "2.2.1", "NAudio.WinMM": "2.2.1"}, "runtime": {"lib/net6.0-windows7.0/NAudio.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NAudio.Asio/2.2.1": {"dependencies": {"Microsoft.Win32.Registry": "4.7.0", "NAudio.Core": "2.2.1"}, "runtime": {"lib/netstandard2.0/NAudio.Asio.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NAudio.Core/2.2.1": {"runtime": {"lib/netstandard2.0/NAudio.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NAudio.Midi/2.2.1": {"dependencies": {"NAudio.Core": "2.2.1"}, "runtime": {"lib/netstandard2.0/NAudio.Midi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NAudio.Wasapi/2.2.1": {"dependencies": {"NAudio.Core": "2.2.1"}, "runtime": {"lib/netstandard2.0/NAudio.Wasapi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NAudio.WinForms/2.2.1": {"dependencies": {"NAudio.WinMM": "2.2.1"}, "runtime": {"lib/netcoreapp3.1/NAudio.WinForms.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NAudio.WinMM/2.2.1": {"dependencies": {"Microsoft.Win32.Registry": "4.7.0", "NAudio.Core": "2.2.1"}, "runtime": {"lib/netstandard2.0/NAudio.WinMM.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.1.25517"}}}, "System.CodeDom/9.0.0": {}, "System.Diagnostics.EventLog/6.0.0": {}, "System.Drawing.Common/9.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "9.0.0"}}, "System.Management/9.0.0": {"dependencies": {"System.CodeDom": "9.0.0"}, "runtime": {"lib/net9.0/System.Management.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Reflection.Metadata/1.6.0": {}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.AccessControl/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}}, "System.Security.Principal.Windows/4.7.0": {}, "System.Text.Encodings.Web/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Json/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "6.0.0"}}, "xunit/2.9.2": {"dependencies": {"xunit.analyzers": "1.16.0", "xunit.assert": "2.9.2", "xunit.core": "2.9.2"}}, "xunit.abstractions/2.0.3": {"runtime": {"lib/netstandard2.0/xunit.abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "xunit.analyzers/1.16.0": {}, "xunit.assert/2.9.2": {"runtime": {"lib/net6.0/xunit.assert.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "xunit.core/2.9.2": {"dependencies": {"xunit.extensibility.core": "2.9.2", "xunit.extensibility.execution": "2.9.2"}}, "xunit.extensibility.core/2.9.2": {"dependencies": {"xunit.abstractions": "2.0.3"}, "runtime": {"lib/netstandard1.1/xunit.core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "xunit.extensibility.execution/2.9.2": {"dependencies": {"xunit.extensibility.core": "2.9.2"}, "runtime": {"lib/netstandard1.1/xunit.execution.dotnet.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "xunit.runner.visualstudio/2.8.2": {}, "vys/1.0.0": {"dependencies": {"Hardcodet.NotifyIcon.Wpf": "1.1.0", "Microsoft.Web.WebView2": "1.0.2792.45", "Microsoft.Web.WebView2.DevToolsProtocolExtension": "1.0.2901", "NAudio": "2.2.1", "System.Drawing.Common": "9.0.0", "System.Management": "9.0.0"}, "runtime": {"vys.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Microsoft.Web.WebView2.Core/1.0.2792.45": {"runtime": {"Microsoft.Web.WebView2.Core.dll": {"assemblyVersion": "1.0.2792.45", "fileVersion": "1.0.2792.45"}}}, "Microsoft.Web.WebView2.WinForms/1.0.2792.45": {"runtime": {"Microsoft.Web.WebView2.WinForms.dll": {"assemblyVersion": "1.0.2792.45", "fileVersion": "1.0.2792.45"}}}, "Microsoft.Web.WebView2.Wpf/1.0.2792.45": {"runtime": {"Microsoft.Web.WebView2.Wpf.dll": {"assemblyVersion": "1.0.2792.45", "fileVersion": "1.0.2792.45"}}}}}, "libraries": {"vys.Tests/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Castle.Core/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-rpYtIczkzGpf+EkZgDr9CClTdemhsrwA/W5hMoPjLkRFnXzH44zDLoovXeKtmxb1ykXK9aJVODSpiJml8CTw2g==", "path": "castle.core/5.1.1", "hashPath": "castle.core.5.1.1.nupkg.sha512"}, "coverlet.collector/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-bJShQ6uWRTQ100ZeyiMqcFlhP7WJ+bCuabUs885dJiBEzMsJMSFr7BOyeCw4rgvQokteGi5rKQTlkhfQPUXg2A==", "path": "coverlet.collector/6.0.2", "hashPath": "coverlet.collector.6.0.2.nupkg.sha512"}, "Hardcodet.NotifyIcon.Wpf/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-FFEkdRSidMrbMfQVwhCzq6KtSyR1qQojqtiUrkCyjnOOuMf7W3TNQkzFnBBxYxFe7uLk5mz7M38wFh1qdWyfvg==", "path": "hardcodet.notifyicon.wpf/1.1.0", "hashPath": "hardcodet.notifyicon.wpf.1.1.0.nupkg.sha512"}, "Microsoft.CodeCoverage/17.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-4svMznBd5JM21JIG2xZKGNanAHNXplxf/kQDFfLHXQ3OnpJkayRK/TjacFjA+EYmoyuNXHo/sOETEfcYtAzIrA==", "path": "microsoft.codecoverage/17.12.0", "hashPath": "microsoft.codecoverage.17.12.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.NET.Test.Sdk/17.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-kt/PKBZ91rFCWxVIJZSgVLk+YR+4KxTuHf799ho8WNiK5ZQpJNAEZCAWX86vcKrs+DiYjiibpYKdGZP6+/N17w==", "path": "microsoft.net.test.sdk/17.12.0", "hashPath": "microsoft.net.test.sdk.17.12.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "path": "microsoft.netcore.platforms/3.1.0", "hashPath": "microsoft.netcore.platforms.3.1.0.nupkg.sha512"}, "Microsoft.TestPlatform.ObjectModel/17.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-TDqkTKLfQuAaPcEb3pDDWnh7b3SyZF+/W9OZvWFp6eJCIiiYFdSB6taE2I6tWrFw5ywhzOb6sreoGJTI6m3rSQ==", "path": "microsoft.testplatform.objectmodel/17.12.0", "hashPath": "microsoft.testplatform.objectmodel.17.12.0.nupkg.sha512"}, "Microsoft.TestPlatform.TestHost/17.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-MiPEJQNyADfwZ4pJNpQex+t9/jOClBGMiCiVVFuELCMSX2nmNfvUor3uFVxNNCg30uxDP8JDYfPnMXQzsfzYyg==", "path": "microsoft.testplatform.testhost/17.12.0", "hashPath": "microsoft.testplatform.testhost.17.12.0.nupkg.sha512"}, "Microsoft.Web.WebView2/1.0.2792.45": {"type": "package", "serviceable": true, "sha512": "sha512-K<PERSON>lLJSq70OySfU8mdhWdh9iOyApazWsIb6CmSz+YTJ5MmwLcsCLMW0qemORo7Si3A7VhLDIH3jwpMhPxodfkuA==", "path": "microsoft.web.webview2/1.0.2792.45", "hashPath": "microsoft.web.webview2.1.0.2792.45.nupkg.sha512"}, "Microsoft.Web.WebView2.DevToolsProtocolExtension/1.0.2901": {"type": "package", "serviceable": true, "sha512": "sha512-DO2lwrgm92LlzxNgkGjUi3Sb3bT74ke0frVWEkfPII4+woCj9MXcyilRGVmh7mENp3A44nDgkcbAgCSK+4KaYA==", "path": "microsoft.web.webview2.devtoolsprotocolextension/1.0.2901", "hashPath": "microsoft.web.webview2.devtoolsprotocolextension.1.0.2901.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-z8FfGIaoeALdD+KF44A2uP8PZIQQtDGiXsOLuN8nohbKhkyKt7zGaZb+fKiCxTuBqG22Q7myIAioSWaIcOOrOw==", "path": "microsoft.win32.systemevents/9.0.0", "hashPath": "microsoft.win32.systemevents.9.0.0.nupkg.sha512"}, "Moq/4.20.72": {"type": "package", "serviceable": true, "sha512": "sha512-EA55cjyNn8eTNWrgrdZJH5QLFp2L43oxl1tlkoYUKIE9pRwL784OWiTXeCV5ApS+AMYEAlt7Fo03A2XfouvHmQ==", "path": "moq/4.20.72", "hashPath": "moq.4.20.72.nupkg.sha512"}, "NAudio/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-c0DzwiyyklM0TP39Y7RObwO3QkWecgM6H60ikiEnsV/aEAJPbj5MFCLaD8BSfKuZe0HGuh9GRGWWlJmSxDc9MA==", "path": "naudio/2.2.1", "hashPath": "naudio.2.2.1.nupkg.sha512"}, "NAudio.Asio/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-hQglyOT5iT3XuGpBP8ZG0+aoqwRfidHjTNehpoWwX0g6KJEgtH2VaqM2nuJ2mheKZa/IBqB4YQTZVvrIapzfOA==", "path": "naudio.asio/2.2.1", "hashPath": "naudio.asio.2.2.1.nupkg.sha512"}, "NAudio.Core/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-GgkdP6K/7FqXFo7uHvoqGZTJvW4z8g2IffhOO4JHaLzKCdDOUEzVKtveoZkCuUX8eV2HAINqi7VFqlFndrnz/g==", "path": "naudio.core/2.2.1", "hashPath": "naudio.core.2.2.1.nupkg.sha512"}, "NAudio.Midi/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-6r23ylGo5aeP02WFXsPquz0T0hFJWyh+7t++tz19tc3Kr38NHm+Z9j+FiAv+xkH8tZqXJqus9Q8p6u7bidIgbw==", "path": "naudio.midi/2.2.1", "hashPath": "naudio.midi.2.2.1.nupkg.sha512"}, "NAudio.Wasapi/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-lFfXoqacZZe0WqNChJgGYI+XV/n/61LzPHT3C1CJp4khoxeo2sziyX5wzNYWeCMNbsWxFvT3b3iXeY1UYjBhZw==", "path": "naudio.wasapi/2.2.1", "hashPath": "naudio.wasapi.2.2.1.nupkg.sha512"}, "NAudio.WinForms/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-DlDkewY1myY0A+3NrYRJD+MZhZV0yy1mNF6dckB27IQ9XCs/My5Ip8BZcoSHOsaPSe2GAjvoaDnk6N9w8xTv7w==", "path": "naudio.winforms/2.2.1", "hashPath": "naudio.winforms.2.2.1.nupkg.sha512"}, "NAudio.WinMM/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-xFHRFwH4x6aq3IxRbewvO33ugJRvZFEOfO62i7uQJRUNW2cnu6BeBTHUS0JD5KBucZbHZaYqxQG8dwZ47ezQuQ==", "path": "naudio.winmm/2.2.1", "hashPath": "naudio.winmm.2.2.1.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "System.CodeDom/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-oTE5IfuMoET8yaZP/vdvy9xO47guAv/rOhe4DODuFBN3ySprcQOlXqO3j+e/H/YpKKR5sglrxRaZ2HYOhNJrqA==", "path": "system.codedom/9.0.0", "hashPath": "system.codedom.9.0.0.nupkg.sha512"}, "System.Diagnostics.EventLog/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lcyUiXTsETK2ALsZrX+nWuHSIQeazhqPphLfaRxzdGaG93+0kELqpgEHtwWOlQe7+jSFnKwaCAgL4kjeZCQJnw==", "path": "system.diagnostics.eventlog/6.0.0", "hashPath": "system.diagnostics.eventlog.6.0.0.nupkg.sha512"}, "System.Drawing.Common/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uoozjI3+dlgKh2onFJcz8aNLh6TRCPlLSh8Dbuljc8CdvqXrxHOVysJlrHvlsOCqceqGBR1wrMPxlnzzhynktw==", "path": "system.drawing.common/9.0.0", "hashPath": "system.drawing.common.9.0.0.nupkg.sha512"}, "System.Management/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bVh4xAMI5grY5GZoklKcMBLirhC8Lqzp63Ft3zXJacwGAlLyFdF4k0qz4pnKIlO6HyL2Z4zqmHm9UkzEo6FFsA==", "path": "system.management/9.0.0", "hashPath": "system.management.9.0.0.nupkg.sha512"}, "System.Reflection.Metadata/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-COC1aiAJjCoA5GBF+QKL2uLqEBew4JsCkQmoHKbN3TlOZKa2fKLz5CpiRQKDz0RsAOEGsVKqOD5bomsXq/4STQ==", "path": "system.reflection.metadata/1.6.0", "hashPath": "system.reflection.metadata.1.6.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.AccessControl/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "path": "system.security.accesscontrol/4.7.0", "hashPath": "system.security.accesscontrol.4.7.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "path": "system.security.principal.windows/4.7.0", "hashPath": "system.security.principal.windows.4.7.0.nupkg.sha512"}, "System.Text.Encodings.Web/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vg8eB5Tawm1IFqj4TVK1czJX89rhFxJo9ELqc/Eiq0eXy13RK00eubyU6TJE6y+GQXjyV5gSfiewDUZjQgSE0w==", "path": "system.text.encodings.web/6.0.0", "hashPath": "system.text.encodings.web.6.0.0.nupkg.sha512"}, "System.Text.Json/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zaJsHfESQvJ11vbXnNlkrR46IaMULk/gHxYsJphzSF+07kTjPHv+Oc14w6QEOfo3Q4hqLJgStUaYB9DBl0TmWg==", "path": "system.text.json/6.0.0", "hashPath": "system.text.json.6.0.0.nupkg.sha512"}, "xunit/2.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-7LhFS2N9Z6Xgg8aE5lY95cneYivRMfRI8v+4PATa4S64D5Z/Plkg0qa8dTRHSiGRgVZ/CL2gEfJDE5AUhOX+2Q==", "path": "xunit/2.9.2", "hashPath": "xunit.2.9.2.nupkg.sha512"}, "xunit.abstractions/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-pot1I4YOxlWjIb5jmwvvQNbTrZ3lJQ+jUGkGjWE3hEFM0l5gOnBWS+H3qsex68s5cO52g+44vpGzhAt+42vwKg==", "path": "xunit.abstractions/2.0.3", "hashPath": "xunit.abstractions.2.0.3.nupkg.sha512"}, "xunit.analyzers/1.16.0": {"type": "package", "serviceable": true, "sha512": "sha512-hptYM7vGr46GUIgZt21YHO4rfuBAQS2eINbFo16CV/Dqq+24Tp+P5gDCACu1AbFfW4Sp/WRfDPSK8fmUUb8s0Q==", "path": "xunit.analyzers/1.16.0", "hashPath": "xunit.analyzers.1.16.0.nupkg.sha512"}, "xunit.assert/2.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-QkNBAQG4pa66cholm28AxijBjrmki98/vsEh4Sx5iplzotvPgpiotcxqJQMRC8d7RV7nIT8ozh97957hDnZwsQ==", "path": "xunit.assert/2.9.2", "hashPath": "xunit.assert.2.9.2.nupkg.sha512"}, "xunit.core/2.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-O6RrNSdmZ0xgEn5kT927PNwog5vxTtKrWMihhhrT0Sg9jQ7iBDciYOwzBgP2krBEk5/GBXI18R1lKvmnxGcb4w==", "path": "xunit.core/2.9.2", "hashPath": "xunit.core.2.9.2.nupkg.sha512"}, "xunit.extensibility.core/2.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-Ol+KlBJz1x8BrdnhN2DeOuLrr1I/cTwtHCggL9BvYqFuVd/TUSzxNT5O0NxCIXth30bsKxgMfdqLTcORtM52yQ==", "path": "xunit.extensibility.core/2.9.2", "hashPath": "xunit.extensibility.core.2.9.2.nupkg.sha512"}, "xunit.extensibility.execution/2.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-rKMpq4GsIUIJibXuZoZ8lYp5EpROlnYaRpwu9Zr0sRZXE7JqJfEEbCsUriZqB+ByXCLFBJyjkTRULMdC+U566g==", "path": "xunit.extensibility.execution/2.9.2", "hashPath": "xunit.extensibility.execution.2.9.2.nupkg.sha512"}, "xunit.runner.visualstudio/2.8.2": {"type": "package", "serviceable": true, "sha512": "sha512-vm1tbfXhFmjFMUmS4M0J0ASXz3/U5XvXBa6DOQUL3fEz4Vt6YPhv+ESCarx6M6D+9kJkJYZKCNvJMas1+nVfmQ==", "path": "xunit.runner.visualstudio/2.8.2", "hashPath": "xunit.runner.visualstudio.2.8.2.nupkg.sha512"}, "vys/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Web.WebView2.Core/1.0.2792.45": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Web.WebView2.WinForms/1.0.2792.45": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Web.WebView2.Wpf/1.0.2792.45": {"type": "reference", "serviceable": false, "sha512": ""}}}