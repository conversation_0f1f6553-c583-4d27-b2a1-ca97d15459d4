// Test script to verify JavaScript exception handling in WebView2
// This script can be executed in the browser console to test exception capture

console.log('🧪 Starting JavaScript exception handling tests...');

// Test 1: TypeError (similar to reCAPTCHA error)
setTimeout(() => {
    console.log('🔴 Test 1: Simulating TypeError like reCAPTCHA error');
    var nullObj = null;
    Object.keys(nullObj); // This will throw: Cannot convert undefined or null to object
}, 1000);

// Test 2: Uncaught TypeError (this should be captured by our exception handler)
setTimeout(() => {
    console.log('🔴 Test 2: Throwing uncaught TypeError');
    var undefinedVar;
    undefinedVar.someProperty.anotherProperty; // Uncaught TypeError
}, 2000);

// Test 3: ReferenceError
setTimeout(() => {
    console.log('🔴 Test 3: Throwing ReferenceError');
    nonExistentFunction(); // ReferenceError: nonExistentFunction is not defined
}, 3000);

// Test 4: Custom error with stack trace
setTimeout(() => {
    console.log('🔴 Test 4: Throwing custom error');
    throw new Error('Custom test error for exception handling verification');
}, 4000);

console.log('🧪 Exception tests scheduled - check C# logs for captured exceptions');
